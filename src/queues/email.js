import { sendEmailTemplate } from '../postmark';
import { getApplicationFromKV } from '../helpers';
import { downloadDocument, arrayBufferToBase64 } from '../pandadoc';
const log = (...args) => console.log('EMAIL:\t', ...args);

/**
 * Handle email queue messages
 * @param {Object} message - The message object containing application data
 * @param {Object} env - Environment variables and bindings
 */
export async function emailQueue<PERSON>andler(message, env) {
  log('emailQueue<PERSON><PERSON>ler called');

  try {
    const { application } = message;
    if (!application) {
      console.error('No application data in message');
      return;
    }

    // check updated app - because we need to see where the app status is *currently*
    const app = await getApplicationFromKV(env, application.uuid);

    log(`Application UUID: ${app.uuid}`);
    log(`Application Status: ${app.status}`);

    // using up-to-date status to know if we should send the email or not
    if (app.status === 'PREQUAL_APPROVED') {
      const placeholders = {
        first_name: application.preQualifyFields.firstName,
        funding_amount: `$${application.approvalAmount.toLocaleString()}`,
        agent: {
          name: application.agent.name,
          email: application.agent.email,
          phone: application.agent.phone,
          image: application.agent.image,
          calendly_url: application.agent.calendlyUrl,
        },
        complete_url: `${env.PORTAL_URL}/application/${application.uuid}`,
      };

      log(`Processing email for application ${application.uuid}`);

      const email = await sendEmailTemplate(env, {
        to: application.preQualifyFields.email,
        template: env.PREQUAL_TEMPLATE,
        placeholders: placeholders,
        replyTo: application.agent.email,
      });

      log(`Email result: ${JSON.stringify(email)}`);
    } // using application status from message since this must always send the merchant their signed doc from pandadoc
    else if (application.status === 'APP_SIGNED') {
      log('Processing APP_SIGNED email to send merchant the signed document from PandaDoc');
      const attachments = [];
      const signedAppBase64 = arrayBufferToBase64(await downloadDocument(env, application.pandadoc.document.id));
      const docName = application.pandadoc.document.name || 'Funding Application';
      attachments.push({
        Name: `${docName}.pdf`,
        Content: signedAppBase64,
        ContentType: 'application/pdf',
      });

      const email = await sendEmailTemplate(env, {
        to: application.applicationFields.businessEmail,
        replyTo: env.ADMIN_EMAIL,
        template: env.DOC_SIGNED_TEMPLATE,
        placeholders: { docName },
        attachments: attachments,
      });

      log(`Signed Doc Email result: ${JSON.stringify(email)}`);
    } else {
      log(`Application is not 'PREQUAL_APPROVED', skipping`);
    }
  } catch (error) {
    console.error(`Error processing email: ${error.message}`);
    throw error;
  }
}
