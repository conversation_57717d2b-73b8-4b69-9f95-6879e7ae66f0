import { Hono } from 'hono';

import { corsMiddleware } from './middlewares/cors';
import { rateLimitMiddleware } from './middlewares/rate-limit';
import { apiBodyLimitMiddleware } from './middlewares/body-limit';
import { attachTimestampMiddleware } from './middlewares/attach-timestamp';
import { emailQueueHandler } from './queues/email';
import { salesforceQueueHandler } from './queues/salesforce';
import { adminQueueHandler } from './queues/admin';
import { createtAppHandlers } from './handlers/app/create-app';
import { getAppHandlers } from './handlers/app/get-app';
import { submitAppHandlers } from './handlers/app/submit-app';
import { editAppHandlers } from './handlers/app/edit-app';
import { signAppHandlers } from './handlers/app/sign-app';
import { completeAppHandlers } from './handlers/app/complete-app';
import { errorHandler } from './handlers/error/error-handler';
import { exportDataHandlers } from './handlers/export-data';

const app = new Hono();

app.use('*', corsMiddleware);
app.use('*', rateLimitMiddleware);
app.use('*', apiBodyLimitMiddleware);
app.use('*', attachTimestampMiddleware);

app.get('/', (c) => c.text('OK'));

app.post('/app', ...createtAppHandlers);
app.get('/app/:uuid', ...getAppHandlers);

app.post('/app/:uuid/submit', ...submitAppHandlers);
app.post('/app/:uuid/edit', ...editAppHandlers);
app.post('/app/:uuid/sign', ...signAppHandlers);
app.post('/app/:uuid/complete', ...completeAppHandlers);

app.get('/export', ...exportDataHandlers);

app.notFound((c) => c.text('Not Found', 404));

app.onError(errorHandler);

export default {
  fetch: app.fetch,

  async queue(batch, env) {
    // disable queue while running tests
    const IS_TESTING = import.meta.env ? import.meta.env?.MODE === 'test' : false;
    if (IS_TESTING) {
      return;
    }
    console.log(`Processing ${batch.messages.length} messages from queue: ${batch.queue}`);

    for (const message of batch.messages) {
      try {
        // Parse the message body if it's a string
        const data = typeof message.body === 'string' ? JSON.parse(message.body) : message.body;

        console.log(`Processing message type: ${data.type}`);

        if (data.type === 'email') {
          await emailQueueHandler(data, env);
        } else if (data.type === 'salesforce') {
          await salesforceQueueHandler(data, env);
        } else if (data.type === 'admin') {
          await adminQueueHandler(data, env);
        } else {
          console.warn(`Unknown message type: ${data.type}`);
        }

        // Acknowledge the message was processed successfully
        message.ack();
      } catch (error) {
        const errorId = crypto.randomUUID().replace(/-/g, '').toUpperCase().slice(0, 10);
        const expirationTtl = env.LOG_KV_TTL_DAYS * 60 * 60 * 24; // seconds

        // Store in KV with TTL
        await env.LOGS.put(`error:${errorId}`, JSON.stringify(error), { expirationTtl });

        console.error(`Error processing message:${errorId} - ${error.message}`);
        // Retry the message
        message.retry();
      }
    }
  },
};
