export class AppError extends Error {
  constructor(message, statusCode = 400, source = 'appError', details = {}) {
    super(message);
    this.name = 'AppError';
    this.statusCode = statusCode;
    this.source = source;
    this.details = details;
    this.timestamp = new Date().toISOString();

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      statusCode: this.statusCode,
      source: this.source,
      details: this.details,
      timestamp: this.timestamp,
      stack: this.stack,
    };
  }
}

export async function getApplicationFromKV(env, uuid) {
  return JSON.parse(await env.KV.get(`app:${uuid}`));
}

export async function updateApplicationInKV(env, uuid, data, timestamp = null) {
  data.updated_at = timestamp || new Date().toISOString();

  const expirationTtl = env.APP_KV_TTL_DAYS * 60 * 60 * 24; // seconds

  await env.KV.put(`app:${uuid}`, JSON.stringify(data), { expirationTtl });
  return data;
}

export function safeUTMObject(utm) {
  if (!utm || typeof utm !== 'object') return null;
  const safeUtm = {};
  for (const [key, value] of Object.entries(utm)) {
    if (typeof value === 'string' && value.length <= 256) {
      safeUtm[key] = value;
    }
  }
  return safeUtm;
}

function pickFields(obj, paths) {
  const result = {};
  // Always include these fields
  paths.push(...['uuid', 'version', 'status', 'created_at', 'updated_at', 'agent', 'approvalAmount']);

  for (const path of paths) {
    const keys = path.split('.');
    let src = obj;
    let dst = result;

    for (let i = 0; i < keys.length; i++) {
      const key = keys[i];
      const arrayMatch = key.match(/^(\w+)\[(\d+)\]$/);

      if (arrayMatch) {
        const [_, arrKey, index] = arrayMatch;
        if (!Array.isArray(src[arrKey]) || src[arrKey][index] == null) break;

        dst[arrKey] ??= [];
        dst = dst[arrKey];
        src = src[arrKey];

        while (dst.length <= index) dst.push(undefined);
        if (i === keys.length - 1) {
          dst[index] = src[index];
        } else {
          dst[index] ??= {};
          dst = dst[index];
          src = src[index];
        }
      } else {
        if (src == null || !(key in src)) break;

        if (i === keys.length - 1) {
          dst[key] = src[key];
        } else {
          dst[key] ??= {};
          dst = dst[key];
          src = src[key];
        }
      }
    }
  }

  return result;
}

export function cleanedApplication(application) {
  const { meta, utm, ...cleaned } = application;

  // remove ID since it's only used internally for Salesforce
  if (cleaned.agent?.id) {
    delete cleaned.agent.id;
  }
  // Remove PII data from applications
  if (cleaned?.applicationFields?.ein) {
    delete cleaned.applicationFields.ein;
  }
  if (cleaned?.applicationFields?.owners) {
    cleaned.applicationFields.owners = cleaned.applicationFields.owners.map((owner) => {
      delete owner?.ssn;
      delete owner?.dateOfBirth;
      return owner;
    });
  }
  if (cleaned?.applicationFields?.bankStatements) {
    delete cleaned.applicationFields.bankStatements;
  }

  // Only return needed fields depending on the status
  const { status } = application;
  let result = {};
  switch (status) {
    case 'PREQUAL_APPROVED':
      result = pickFields(cleaned, ['preQualifyFields']);
      break;
    case 'PREQUAL_DENIED':
      result = pickFields(cleaned, ['reason', 'preQualifyFields.firstName', 'preQualifyFields.businessName']);
      break;
    case 'APP_SUBMITTED':
      result = pickFields(cleaned, ['pandadoc', 'preQualifyFields.firstName', 'preQualifyFields.businessName']);
      break;
    case 'APP_EDITING':
      result = pickFields(cleaned, ['preQualifyFields', 'applicationFields']);
      break;
    case 'APP_SIGNED':
      result = pickFields(cleaned, ['preQualifyFields.firstName', 'preQualifyFields.businessName']);
      break;
    case 'APP_COMPLETED':
      result = pickFields(cleaned, [
        'preQualifyFields.fundingAmount',
        'applicationFields.owners[0].firstName',
        'applicationFields.businessName',
      ]);
      break;
    default:
      result = pickFields(cleaned, []);
      break;
  }

  return result;
}

export async function putAppPII(env, uuid, data) {
  if (!uuid) throw new AppError('Missing UUID', 400, 'putAppPII');
  if (!data) throw new AppError('Missing PII data', 400, 'putAppPII');
  data.version = env.VERSION;

  // 24 hours in seconds
  const expirationTtl = 24 * 60 * 60;

  await env.KV.put(`app:${uuid}:pii`, JSON.stringify(data), { expirationTtl });
  return data;
}

export async function getAppPII(env, uuid) {
  if (!uuid) throw new AppError('Missing UUID', 400, 'getAppPII');

  let piiData = {};
  try {
    const result = await env.KV.get(`app:${uuid}:pii`, { type: 'json' });
    if (result) piiData = result;
  } catch (error) {
    console.error(`Error getting PII: ${error.message}`);
    piiData = {};
  }

  return piiData;
}

export async function putAppBankStatements(env, uuid, data) {
  if (!uuid) throw new AppError('Missing UUID', 400, 'putAppBankStatements');
  if (!data) throw new AppError('Missing bank statements data', 400, 'putAppBankStatements');
  data.version = env.VERSION;

  const expirationTtl = env.APP_KV_TTL_DAYS * 60 * 60 * 24; // seconds

  await env.KV.put(`app:${uuid}:bank-statements`, JSON.stringify(data), { expirationTtl });
  return data;
}

export async function getAppBankStatements(env, uuid) {
  if (!uuid) throw new AppError('Missing UUID', 400, 'getAppBankStatements');

  let bankStatements = [];
  try {
    const data = await env.KV.get(`app:${uuid}:bank-statements`, { type: 'json' });
    if (data && data.length) {
      bankStatements = data;
    }
  } catch (error) {
    console.error(`Error getting bank statements: ${error.message}`);
    bankStatements = [];
  }
  return bankStatements;
}

export function extractAndSanitizePIIFields(applicationFields) {
  // Extract PII data
  const piiData = {
    ein: applicationFields.ein,
    owners:
      applicationFields.owners?.map((owner) => ({
        dateOfBirth: owner.dateOfBirth,
        ssn: owner.ssn,
      })) || [],
  };

  const bankStatements = applicationFields.bankStatements || [];

  // Remove PII data from applicationFields
  const sanitizedApplicationFields = { ...applicationFields };
  delete sanitizedApplicationFields.ein;
  delete sanitizedApplicationFields.bankStatements;

  if (sanitizedApplicationFields.owners && sanitizedApplicationFields.owners.length > 0) {
    sanitizedApplicationFields.owners = applicationFields.owners.map((owner) => {
      const sanitizedOwner = { ...owner };
      delete sanitizedOwner.dateOfBirth;
      delete sanitizedOwner.ssn;
      return sanitizedOwner;
    });
  }

  return {
    piiData,
    bankStatements,
    sanitizedApplicationFields,
  };
}

export function mergeAppFieldsWithPII(applicationFields, piiData = {}, bankStatements = []) {
  const mergedApplicationFields = { ...applicationFields };

  if (piiData.ein) {
    mergedApplicationFields.ein = piiData.ein;
  }

  if (piiData.owners && piiData.owners.length > 0) {
    mergedApplicationFields.owners = applicationFields.owners.map((owner, index) => {
      const piiOwner = piiData.owners[index] || {};
      return {
        ...owner,
        ...piiOwner,
      };
    });
  }

  mergedApplicationFields.bankStatements = bankStatements?.length ? bankStatements : [];

  return mergedApplicationFields;
}
