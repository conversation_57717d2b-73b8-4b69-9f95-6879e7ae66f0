import { createFactory } from 'hono/factory';
import { ensureApplicationByUUID } from './get-app';
import { AppError, cleanedApplication, updateApplicationInKV } from '../../helpers';
import { getMeta } from '../../utils';

const factory = createFactory();

export const signAppHandlers = factory.createHandlers(ensureApplicationByUUID, async (c) => {
  const uuid = c.req.param('uuid');
  const timestamp = c.get('timestamp');
  const application = c.get('application');

  if (application.status !== 'APP_SUBMITTED') {
    throw new AppError("Application can't be signed", 400, 'signApp');
  }

  application.signed_at = timestamp;
  application.status = 'APP_SIGNED';
  application.meta.signed = getMeta(c.req.raw, timestamp);

  await Promise.all([
    updateApplicationInKV(c.env, uuid, application, timestamp),
    c.env.QUEUE.send({ type: 'email', application }),
    // await c.env.QUEUE.send({ type: 'salesforce', application });
  ]);

  return c.json({ data: cleanedApplication(application) });
});
