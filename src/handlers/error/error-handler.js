import { AppError } from '../../helpers';

export const errorHandler = async (err, c) => {
  const errorId = crypto.randomUUID().replace(/-/g, '').toUpperCase().slice(0, 10);
  const expirationTtl = c.env.LOG_KV_TTL_DAYS * 60 * 60 * 24; // seconds

  const headers = Object.fromEntries(c.req.raw.headers);
  const body = headers['content-type']?.includes('application/json') ? await c.req.bodyCache.json : await c.req.bodyCache.text;

  const errorData = {
    id: errorId,
    timestamp: new Date().toISOString(),
    headers,
    cf: c.req.raw.cf,
    requestBody: body || null,
    source: err?.source,
    message: err?.message,
    stack: err?.stack,
    statusCode: err?.statusCode,
    details: JSON.stringify(err),
  };

  console.error('Error ID:', errorId);
  console.error(err.message, err.stack);

  // Store in KV with TTL
  await c.env.LOGS.put(`error:${errorId}`, JSON.stringify(errorData), { expirationTtl });

  if (err instanceof AppError) {
    console.error(err);
    return c.json({ errorId, error: err.message }, err.statusCode);
  } else {
    console.error(err);
    return c.json({ errorId, error: 'Internal Server Error' }, 500);
  }
};
