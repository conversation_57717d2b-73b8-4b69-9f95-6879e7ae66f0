import { PDFDocument } from 'pdf-lib';
import { env } from 'hono/adapter';

const log = (...args) => console.log('PANDADOC:\t', ...args);
const error = (...args) => console.error('PANDADOC:\t', ...args);

export function arrayBufferToBase64(buffer) {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let b of bytes) {
    binary += String.fromCharCode(b);
  }
  return btoa(binary);
}

/**
 * Makes an authenticated request to the PandaDoc API
 * @param {string} endpoint - API endpoint path, e.g. '/documents'
 * @param {Object} options - fetch options (method, headers, body, etc.)
 * @returns {Promise<Object>} - JSON response from PandaDoc
 * @throws {Error} - On non-OK responses or network issues
 */
async function pandaDocRequest(env, endpoint, options = {}) {
  const url = `${env.PANDADOC_API_BASE}${endpoint}`;

  const headers = {
    Authorization: `API-Key ${env.PANDADOC_API_KEY}`,
    'Content-Type': 'application/json',
    ...(options.headers || {}),
  };

  const res = await fetch(url, {
    ...options,
    headers,
  });

  if (!res.ok) {
    const text = await res.text();
    error(res.status, text);
    throw new Error(`PandaDoc Error`, { cause: { status: res.status, text } });
  }

  if (res.status === 204) return {}; // No content
  if (res.headers.get('content-type')?.includes('application/pdf')) return await res.arrayBuffer();
  return await res.json();
}

function generatePandaDocFields(application) {
  const { uuid, preQualifyFields: prequal, applicationFields: app } = application;

  const documentFields = [
    { name: 'applicationId', value: `${uuid}` },
    // Business Info
    { name: 'businessName', value: app.businessName },
    { name: 'dbaName', value: app.dbaName },
    { name: 'businessAddress', value: `${app.address.line1} ${app.address.line2}` },
    { name: 'businessCity', value: app.address.city },
    { name: 'businessState', value: app.address.state },
    { name: 'businessZip', value: app.address.zip },
    // DISABLED at request of client
    // { name: 'businessEmail', value: app.businessEmail },
    // { name: 'businessPhone', value: app.businessPhone },
    { name: 'entityType', value: app.entityType },
    { name: 'industry', value: app.industry },
    { name: 'businessStartDate', value: new Date(prequal.businessStartDate).toLocaleDateString('en-US') },
    { name: 'ein', value: app.ein },
    // Owner #1 Info
    { name: 'owners.0.fullName', value: `${app.owners[0].firstName} ${app.owners[0].lastName}` },
    { name: 'owners.0.address', value: `${app.owners[0].address.line1} ${app.owners[0].address.line2}` },
    { name: 'owners.0.city', value: app.owners[0].address.city },
    { name: 'owners.0.state', value: app.owners[0].address.state },
    { name: 'owners.0.zip', value: app.owners[0].address.zip },
    { name: 'owners.0.ownershipPercentage', value: app.owners[0].ownershipPercentage },
    { name: 'owners.0.dateOfBirth', value: new Date(app.owners[0].dateOfBirth).toLocaleDateString('en-US') },
    { name: 'owners.0.ssn', value: app.owners[0].ssn },
    { name: 'owners.0.creditScore', value: prequal.estimatedFICO },
    // Owner #2 Info
  ];

  if (app.owners.length > 1 && app.owners[1]) {
    documentFields.push(
      ...[
        { name: 'owners.1.fullName', value: `${app.owners[1].firstName} ${app.owners[1].lastName}` },
        { name: 'owners.1.address', value: `${app.owners[1].address.line1} ${app.owners[1].address.line2}` },
        {
          name: 'owners.1.cityStateZip',
          value: `${app.owners[1].address.city},${app.owners[1].address.state}, ${app.owners[1].address.zip}`,
        },
        { name: 'owners.1.ownershipPercentage', value: app.owners[1].ownershipPercentage },
        { name: 'owners.1.dateOfBirth', value: new Date(app.owners[1].dateOfBirth).toLocaleDateString('en-US') },
        { name: 'owners.1.ssn', value: app.owners[1].ssn },
        { name: 'owners.1.creditScore', value: '' },
      ]
    );
  }

  return documentFields;
}

const IS_TESTING = env(import.meta) ? env(import.meta)?.MODE === 'test' : false;
// use pinnacle email while running tests or in dev mode (since pandadoc doesn't allow random emails in sandbox)
function generatePandaDocRecipients(env, application) {
  const { applicationFields: app, preQualifyFields: prequal } = application;
  const recipients = [
    {
      email: env.DEV_MODE || IS_TESTING ? '<EMAIL>' : app.owners[0].email || prequal.email,
      first_name: app.owners[0].firstName || prequal.firstName,
      last_name: app.owners[0].lastName || prequal.lastName,
      role: 'Merchant',
    },
  ];
  return recipients;
}

export async function createPandaDocFromTemplate(env, application) {
  const { uuid, preQualifyFields: prequal, applicationFields: app, agent } = application;

  const doc = await pandaDocRequest(env, '/documents', {
    method: 'POST',
    body: JSON.stringify({
      name: `Funding Application - ${app?.businessName || prequal.businessName}`,
      template_uuid: env.PANDADOC_TEMPLATE_UUID,
      folder_uuid: env.PANDADOC_FOLDER_UUID,
      tags: [agent.email],
      recipients: generatePandaDocRecipients(env, application),
      tokens: generatePandaDocFields(application),
      metadata: { applicationId: uuid, agent: agent.email, created_via: 'portal' },
    }),
  });

  return doc;
}

export async function updatePandaDocFields(env, application) {
  const documentId = application.pandadoc.document.id;
  const { applicationFields: app } = application;

  const doc = await pandaDocRequest(env, `/documents/${documentId}`, {
    method: 'PATCH',
    body: JSON.stringify({
      name: `Funding Application - ${app.businessName}`,
      tokens: generatePandaDocFields(application),
    }),
  });

  return doc;
}

export async function moveDocumentToDraft(env, documentId) {
  return await pandaDocRequest(env, `/documents/${documentId}/draft`, {
    method: 'POST',
  });
}

export async function downloadDocument(env, documentId) {
  return await pandaDocRequest(env, `/documents//${documentId}/download`, {
    method: 'GET',
  });
}

export async function downloadDocumentWithoutSignature(env, documentId) {
  const pdfBytes = await downloadDocument(env, documentId);
  const pdfDoc = await PDFDocument.load(pdfBytes);
  const totalPages = pdfDoc.getPageCount();
  if (totalPages > 1) {
    // if there is more than 1 page - remove last page (the signature page)
    pdfDoc.removePage(totalPages - 1);
  }
  const modifiedPdf = await pdfDoc.save();
  const base64 = arrayBufferToBase64(modifiedPdf.buffer);
  return base64;
}

/**
 * Creates a PandaDoc document from a template, waits until it's ready, sends it silently,
 * and creates a session for the recipient.
 *
 * @param {Object} application - Application data with fields needed for template fill
 * @returns {Promise<string>} - The session ID
 */
export async function createPandaDocSession(env, application) {
  // Step 1: Create the document from template
  // TODO: handle document creation seperately from session generating - then store document ID on the application
  /*
    In the future... we can even generate the document on application begin (pre-submission)
    at submission we just need to send an update request with the fields and generate the session
    this would shave off several seconds.
    For now we will call both functions at submission (create doc and session).
    If the user is on APP_EDITING (meaning they went back) - we will just use the document ID from before, call an udpate and generate session
  */
  let document = await createPandaDocFromTemplate(env, application);
  const documentId = document.id;

  log(`Created Document from template ${JSON.stringify(document)}`);

  // Step 2: Poll until status is "document.draft"
  let retries = 0;
  const maxRetries = 10;
  const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

  while (retries < maxRetries) {
    document = await pandaDocRequest(env, `/documents/${documentId}`);

    log(`Document Status is ${document.status}`);

    if (document.status === 'document.draft') {
      break;
    }

    retries++;
    await delay(200);
  }

  if (retries === maxRetries) {
    throw new Error('PandaDoc document creation timed out.', { cause: { status: document.status, document } });
  }

  // Step 3: Send document silently
  await sendPandaDocSilently(env, documentId);

  log(`Document sent silently`);

  // Step 4: Create a session
  const session = await createEmbeddedSession(env, documentId, application);

  log(`Session: ${JSON.stringify(session)}`);

  return { document, session };
}

export async function createEmbeddedSession(env, documentId, application) {
  const session = await pandaDocRequest(env, `/documents/${documentId}/session`, {
    method: 'POST',
    body: JSON.stringify({
      recipient: generatePandaDocRecipients(env, application)[0].email,
      lifetime: 60 * 60 * 24 * 7,
    }),
  });

  return session;
}

export async function sendPandaDocSilently(env, documentId) {
  await pandaDocRequest(env, `/documents/${documentId}/send`, {
    method: 'POST',
    body: JSON.stringify({ silent: true }),
  });
}
